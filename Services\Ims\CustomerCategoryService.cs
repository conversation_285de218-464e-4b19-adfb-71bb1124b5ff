using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Services.Ims;

/// <summary> 客戶分類服務 </summary>
public class CustomerCategoryService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : ICustomerCategoryService
{
    private const int MaxAllowedDepth = 3;

    /// <summary> 取得所有客戶分類 </summary>
    public async Task<List<CustomerCategoryDTO>> GetAllAsync()
    {
        var entities = await _context.Ims_CustomerCategory
            .Where(c => !c.IsDeleted)
            .Select(c => new CustomerCategoryDTO
            {
                CustomerCategoryID = c.CustomerCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return entities;
    }

    /// <summary> 根據ID取得客戶分類 </summary>
    public async Task<CustomerCategoryDTO?> GetByIdAsync(Guid customerCategoryId)
    {
        var entity = await _context.Ims_CustomerCategory
            .Where(c => c.CustomerCategoryID == customerCategoryId && !c.IsDeleted)
            .Select(c => new CustomerCategoryDTO
            {
                CustomerCategoryID = c.CustomerCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted
            })
            .FirstOrDefaultAsync();

        return entity;
    }

    /// <summary> 新增客戶分類 </summary>
    public async Task<(bool Success, string Message)> AddAsync(CustomerCategoryDTO dto)
    {
        if (dto == null)
        {
            return (false, "客戶分類資料不可為空");
        }

        // 檢查名稱重複
        if (await _context.Ims_CustomerCategory.AnyAsync(c => c.Name == dto.Name && !c.IsDeleted))
        {
            return (false, "分類名稱已存在");
        }

        // 驗證 ParentID 有效性
        if (dto.ParentID.HasValue)
        {
            if (!await _context.Ims_CustomerCategory.AnyAsync(c => c.CustomerCategoryID == dto.ParentID.Value && !c.IsDeleted))
            {
                return (false, "指定的父分類不存在");
            }

            if (await CheckLevelAsync(dto.ParentID.Value, MaxAllowedDepth))
            {
                return (false, $"客戶分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
            }
        }

        var entity = new CustomerCategory
        {
            CustomerCategoryID = Guid.NewGuid(),
            Name = dto.Name,
            Description = dto.Description,
            ParentID = dto.ParentID,
            SortCode = dto.SortCode,
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId,
            IsDeleted = false
        };

        _context.Ims_CustomerCategory.Add(entity);
        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"新增客戶分類成功: {entity.Name}", "CustomerCategoryService");
        return (true, "新增客戶分類成功");
    }

    /// <summary> 更新客戶分類 </summary>
    public async Task<(bool Success, string Message)> UpdateAsync(CustomerCategoryDTO dto)
    {
        if (dto == null)
        {
            return (false, "客戶分類資料不可為空");
        }

        var entity = await _context.Ims_CustomerCategory
            .FirstOrDefaultAsync(c => c.CustomerCategoryID == dto.CustomerCategoryID && !c.IsDeleted);

        if (entity == null)
        {
            return (false, "找不到指定的客戶分類");
        }

        // 檢查名稱重複（排除自己）
        if (await _context.Ims_CustomerCategory.AnyAsync(c => c.Name == dto.Name && c.CustomerCategoryID != dto.CustomerCategoryID && !c.IsDeleted))
        {
            return (false, "分類名稱已存在");
        }

        // 驗證 ParentID 有效性
        if (dto.ParentID.HasValue)
        {
            if (dto.ParentID == dto.CustomerCategoryID)
            {
                return (false, "不能將自己設為父分類");
            }

            if (!await _context.Ims_CustomerCategory.AnyAsync(c => c.CustomerCategoryID == dto.ParentID.Value && !c.IsDeleted))
            {
                return (false, "指定的父分類不存在");
            }

            if (await CheckLevelAsync(dto.ParentID.Value, MaxAllowedDepth))
            {
                return (false, $"客戶分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
            }
        }

        entity.Name = dto.Name;
        entity.Description = dto.Description;
        entity.ParentID = dto.ParentID;
        entity.SortCode = dto.SortCode;
        entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.UpdateUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"更新客戶分類成功: {entity.Name}", "CustomerCategoryService");
        return (true, "更新客戶分類成功");
    }

    /// <summary> 刪除客戶分類 </summary>
    public async Task<(bool Success, string Message)> DeleteAsync(Guid customerCategoryId)
    {
        var entity = await _context.Ims_CustomerCategory
            .FirstOrDefaultAsync(c => c.CustomerCategoryID == customerCategoryId && !c.IsDeleted);

        if (entity == null)
        {
            return (false, "找不到指定的客戶分類");
        }

        // 檢查是否有子分類
        // if (await _context.Ims_CustomerCategory.AnyAsync(c => c.ParentID == customerCategoryId && !c.IsDeleted))
        // {
        //     return (false, "此分類下還有子分類，無法刪除");
        // }

        // 檢查是否有客戶使用此分類
        // if (await _context.Ims_CustomerDetail.AnyAsync(c => c.CustomerCategoryID == customerCategoryId))
        // {
        //     return (false, "此分類下還有客戶，無法刪除");
        // }

        entity.IsDeleted = true;
        entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.DeleteUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"刪除客戶分類成功: {entity.Name}", "CustomerCategoryService");
        return (true, "刪除客戶分類成功");
    }

    /// <summary> 檢查分類層級 </summary>
    public async Task<bool> CheckLevelAsync(Guid? parentId, int maxDepth)
    {
        if (!parentId.HasValue || maxDepth <= 0)
            return false;

        var currentDepth = 1;
        var currentParentId = parentId;

        while (currentParentId.HasValue && currentDepth < maxDepth)
        {
            var parent = await _context.Ims_CustomerCategory
                .Where(c => c.CustomerCategoryID == currentParentId.Value && !c.IsDeleted)
                .Select(c => c.ParentID)
                .FirstOrDefaultAsync();

            if (parent == null)
                break;

            currentParentId = parent;
            currentDepth++;
        }

        return currentDepth >= maxDepth;
    }

    /// <summary> 取得子分類 </summary>
    public async Task<List<CustomerCategoryDTO>> GetChildrenAsync(Guid parentId)
    {
        var children = await _context.Ims_CustomerCategory
            .Where(c => c.ParentID == parentId && !c.IsDeleted)
            .Select(c => new CustomerCategoryDTO
            {
                CustomerCategoryID = c.CustomerCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return children;
    }

    /// <summary> 取得根分類 </summary>
    public async Task<List<CustomerCategoryDTO>> GetRootCategoriesAsync()
    {
        var rootCategories = await _context.Ims_CustomerCategory
            .Where(c => c.ParentID == null && !c.IsDeleted)
            .Select(c => new CustomerCategoryDTO
            {
                CustomerCategoryID = c.CustomerCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return rootCategories;
    }
}
