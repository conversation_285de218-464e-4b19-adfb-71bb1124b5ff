/**
 * 統一的篩選組件類型定義
 * 
 * 此文件定義了所有篩選相關組件使用的統一類型介面，
 * 確保類型安全和介面一致性。
 */

// 基礎篩選狀態介面
export interface UnifiedFilterState {
  /** 搜尋文字 */
  searchText: string;
  /** 活躍的篩選條件鍵值 */
  activeFilters: string[];
  /** 篩選條件的值 */
  filterValues: Record<string, any>;
}

// 篩選狀態變更事件
export interface FilterStateChangeEvent extends UnifiedFilterState {
  /** 觸發變更的篩選鍵值 */
  filterKey?: string;
  /** 變更的值 */
  value?: any;
  /** 是否有活躍的篩選條件 */
  hasActiveFilters: boolean;
  /** 篩選條件數量 */
  filterCount: number;
}

// 統一的回調介面
export interface UnifiedFilterCallbacks {
  /** 搜尋文字變更回調 */
  onSearchChange?: (text: string) => void;
  /** 篩選條件變更回調 */
  onFilterChange?: (event: FilterStateChangeEvent) => void;
  /** 清除所有篩選回調 */
  onClear?: () => void;
  /** 篩選結果變更回調（用於 FilterSearchContainer） */
  onFilterResult?: (state: FilterStateChangeEvent) => void;
}

// 篩選選項配置
export interface FilterOption {
  /** 顯示標籤 */
  label: string;
  /** 篩選鍵值 */
  value: string;
  /** 篩選類型 */
  type?: 'input' | 'select' | 'treeSelect';
  /** 選項子項目（用於 select 類型） */
  children?: Array<{ label: string; value: string }>;
  /** 樹狀資料（用於 treeSelect 類型） */
  treeData?: any[];
  /** 佔位符文字 */
  placeholder?: string;
  /** 控制項寬度 */
  width?: number;
}

// 外部狀態管理介面
export interface ExternalFilterState {
  /** 活躍的篩選條件 */
  activeFilters: string[];
  /** 篩選條件的值 */
  filterValues: Record<string, any>;
}

// 外部狀態變更回調
export interface ExternalFilterStateCallbacks {
  /** 外部狀態變更回調 */
  onExternalStateChange?: (state: ExternalFilterState) => void;
}

// 組件通用 Props
export interface BaseFilterProps {
  /** 篩選選項配置 */
  filterOptions: FilterOption[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否緊湊模式 */
  compact?: boolean;
  /** 自定義樣式類名 */
  className?: string;
  /** 佈局方向 */
  layout?: 'horizontal' | 'vertical';
}

// AdvancedFilterComponent 專用 Props
export interface AdvancedFilterProps extends BaseFilterProps, UnifiedFilterCallbacks {
  /** 初始活躍篩選條件 */
  initialActiveFilters?: string[];
  /** 初始篩選值 */
  initialFilterValues?: Record<string, any>;
  /** 是否顯示清除按鈕 */
  showClearButton?: boolean;
  /** 是否只顯示篩選按鈕（不顯示篩選條件） */
  buttonOnly?: boolean;

  // 新增：外部狀態支援
  /** 外部狀態（優先於 initial 值） */
  externalState?: ExternalFilterState;
  /** 外部狀態變更回調 */
  onExternalStateChange?: (state: ExternalFilterState) => void;

}

// FilterSearchContainer 專用 Props
export interface FilterSearchContainerProps extends BaseFilterProps, UnifiedFilterCallbacks {
  /** 搜尋框佔位符 */
  searchPlaceholder?: string;
  /** 標題 */
  title?: string;
  /** 是否顯示統計資訊 */
  showStats?: boolean;
  /** 統計資訊 */
  stats?: {
    total: number;
    filtered: number;
  };
  /** 初始搜尋文字 */
  initialSearchText?: string;
  /** 初始篩選條件 */
  initialFilters?: ExternalFilterState;
  /** 清除時是否顯示成功訊息 */
  showClearMessage?: boolean;
  /** 自定義清除訊息 */
  clearMessage?: string;
}

// useFilterSearch Hook 選項
export interface UseFilterSearchOptions extends UnifiedFilterCallbacks {
  /** 初始搜尋文字 */
  initialSearchText?: string;
  /** 初始篩選條件 */
  initialFilters?: ExternalFilterState;
  /** 清除時是否顯示成功訊息 */
  showClearMessage?: boolean;
  /** 自定義清除訊息 */
  clearMessage?: string;
}

// useFilterSearch Hook 返回值
export interface UseFilterSearchReturn extends UnifiedFilterState {
  /** 設定搜尋文字 */
  setSearchText: (text: string) => void;
  /** 設定篩選資料 */
  setFilterData: (data: ExternalFilterState) => void;
  /** 清除所有篩選 */
  clearAll: () => void;
  /** 更新單個篩選條件 */
  updateFilter: (filterKey: string, value: any) => void;
  /** 移除單個篩選條件 */
  removeFilter: (filterKey: string) => void;
  /** 是否有活躍的篩選條件 */
  hasActiveFilters: boolean;
  /** 篩選條件數量 */
  filterCount: number;
  /** 重置所有狀態 */
  reset: () => void;
}

// 常用的篩選選項模板
export const CommonFilterOptions = {
  /** 文字輸入篩選 */
  textInput: (label: string, value: string, placeholder?: string): FilterOption => ({
    label,
    value,
    type: 'input',
    placeholder: placeholder || `輸入${label}`
  }),

  /** 狀態選擇篩選 */
  statusSelect: (label: string, value: string, options: Array<{ label: string; value: string }>): FilterOption => ({
    label,
    value,
    type: 'select',
    children: options
  }),

  /** 樹狀選擇篩選 */
  treeSelect: (label: string, value: string, treeData: any[], width?: number): FilterOption => ({
    label,
    value,
    type: 'treeSelect',
    treeData,
    width: width || 250
  })
};

// 常用的篩選邏輯工具函數
export const FilterUtils = {
  /** 檢查篩選值是否有效 */
  isValidFilterValue: (value: any): boolean => {
    if (value === undefined || value === null || value === '') {
      return false;
    }
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }
    return true;
  },

  /** 計算篩選條件數量 */
  getFilterCount: (state: UnifiedFilterState): number => {
    return state.activeFilters.length + (state.searchText.trim().length > 0 ? 1 : 0);
  },

  /** 檢查是否有活躍的篩選條件 */
  hasActiveFilters: (state: UnifiedFilterState): boolean => {
    return state.activeFilters.length > 0 || state.searchText.trim().length > 0;
  },

  /** 創建篩選狀態變更事件 */
  createFilterStateChangeEvent: (
    state: UnifiedFilterState,
    filterKey?: string,
    value?: any
  ): FilterStateChangeEvent => ({
    ...state,
    filterKey,
    value,
    hasActiveFilters: FilterUtils.hasActiveFilters(state),
    filterCount: FilterUtils.getFilterCount(state)
  })
};
